<transition :name="transitionDirection" mode="out-in">
  <div>
    <link rel="stylesheet" href="component/zuheka/zuheka.css" />
    <div class="main-right pt-2 pr-4 space-x-2 box-border">
      <!--选择订单内容-->
      <div class="o-1vh bg-white rounded-lg flex-1 flex flex-col">
        <div class="o-title-box-shadow shrink-0 p-4">
          <div class="el-input el-input--suffix">
            <input
              type="text"
              autocomplete="off"
              placeholder="请输入服务名称"
              name="search_keyword"
              class="el-input__inner"
              v-model.trim="search_keyword"
              ref="search_keyword"
              @keyup.enter.exact.stop="billingInquiryEnter"
            />
            <span class="el-input__suffix" @click="billingInquiryEnter">
              <span class="el-input__suffix-inner">
                <i class="el-input__icon el-icon-search"></i>
              </span>
            </span>
          </div>
        </div>
        <div
          class="o-scrollbar flex-1 h-0 overflow-y-auto p-4"
          ref="serverCardWrap"
        >
          <div
            class="o-little-server-card-grid-box gap-2"
            v-infinite-scroll="loadMoreProduct"
            infinite-scroll-disabled="isServerScroll"
            infinite-scroll-distance="10"
            infinite-scroll-immediate-check="isServerScroll"
          >
            <div
              class="py-2 px-4 rounded bg-gray-50 border border-solid border-gray-200 hover:border-primary cursor-pointer transition"
              v-for="(value,index) in zhk_server_name"
              @click="bind_zhk_add_server(value)"
            >
              <div class="flex items-center h-12">
                <div class="font-bold text-lg leading-5 line-clamp-2">
                  {{value.service_name}}
                </div>
              </div>
              <div>
                <span class="text-xs pr-1">￥</span>
                {{value.price}}
              </div>
            </div>
            <!-- 触底效果 -->
            <div v-if="busy" class="loadingtip">{{loadingtip}}</div>
          </div>
        </div>
      </div>
      <!--开单内容-->
      <div
        class="o-1vh bg-white rounded-lg flex flex-col shrink-0"
        style="width: 650px"
      >
        <member-search-bar :login-info="loginInfo" />
        <div class="o-scrollbar h-0 flex-1 overflow-y-auto">
          <div class="p-4 space-y-3">
            <div
              class="rounded overflow-hidden bg-white border border-solid border-gray-200"
              v-for="(item,index) in zhk_server_details_name"
            >
              <div
                class="o-card-bg-primary-light px-6 pt-6 pb-3 border-b-solid border-b-1 border-b-gray-200"
              >
                <div class="flex items-center">
                  <div class="text-xl font-bold pr-4">
                    {{item.service_name}}
                  </div>
                  <div class="o-tag o-tag-indigo text-xs">服务</div>
                  <div class="flex-1 flex items-center justify-end pl-6 pr-4">
                    <div
                      class="el-icon-delete cursor-pointer hover:text-primary"
                      @click="zhk_open_details_price_del(index)"
                    ></div>
                  </div>
                </div>
                <div class="mt-2 flex items-center">
                  <div
                    class="text-xs px-4 py-1 border-solid border-gray-200 text-gray-500"
                  >
                    <div class="pr-2 border-r-solid bg-gray-50">原价</div>
                    <div class="pl-2">￥{{}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--选择销售模态框-->
      <el-dialog
        title="选择协助销售"
        :visible.sync="helpStaffVisible"
        width="35%"
        top="7vh"
      >
        <div style="height: calc(100vh - 500px); overflow: auto">
          <el-checkbox-group v-model="checkHelpStaffArr">
            <template v-for="(helpStaff,index) in helpStaffAll">
              <div class="xuazne_xiaoshou" v-if="bindStaffId!=helpStaff.id">
                <el-checkbox
                  :label="helpStaff"
                  style="height: 25px; width: 25px"
                >
                  {{helpStaff.nickname}} ({{helpStaff.job_num}})
                </el-checkbox>
              </div>
            </template>
          </el-checkbox-group>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="cancel-btn" @click="closeHelpStaffVisible(0)">
            取消
          </el-button>
          <el-button type="primary" @click="closeHelpStaffVisible(1)">
            确定
          </el-button>
        </span>
      </el-dialog>

      <!--选择销售模态框-->
      <el-dialog
        title="选择销售"
        :visible.sync="zhk_xiao_shou"
        width="35%"
        top="7vh"
        :append-to-body="true"
      >
        <!--<div class="xuanze_jishi_search">-->
        <!--<el-input placeholder="输入销售名称" suffix-icon="el-icon-search"></el-input>-->
        <!--</div>-->
        <div style="height: calc(100vh - 500px); overflow: auto">
          <div class="xuazne_xiaoshou" v-for="(value , index) in zhkxiaoshous ">
            <el-checkbox
              v-model="value.is_choice_xiaoshou"
              style="height: 25px; width: 25px"
              @change="chioce_xiaoshou(index,value.is_choice_xiaoshou,value.id)"
            >
              {{value.nickname}}
            </el-checkbox>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="cancel-btn" @click="zhk_xiaoshou_over">
            取消
          </el-button>
          <el-button type="primary" @click="zhk_xiaoshou_save">确定</el-button>
        </span>
      </el-dialog>
      <!-- 点击充值收款后出现开单框 -->

      <template v-if="buy_receipt">
        <app-pay
          :buy-receipt="buy_receipt"
          :login-info="loginInfo"
          :use-card="isRechargeCard"
          :order-no="orderNo"
          :bill-to-pay="billToPay"
          :is-pay-status="isPayStatus"
          @close-pay="bindClosePay"
        ></app-pay>
      </template>

      <!-- 客户列表框 -->
      <member-search-dialog
        :value="isShowMemberSearch"
        :login-info="loginInfo"
        @sync-is-show-memeber-search="isShowMemberSearch = $event"
        @handle-select-member="handleSelect"
      ></member-search-dialog>
    </div>
  </div>
</transition>
